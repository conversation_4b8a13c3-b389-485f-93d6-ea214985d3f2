import { ChevronRightIcon, DoubleChevronRightIcon } from "Assets/svgs";
import React, { useState } from "react";
import LimitSelect from "./LimitSelect";
const PaginationBar = ({
  currentPage,
  pageCount,
  pageSize,
  canPreviousPage,
  canNextPage,
  updatePageSize,
  previousPage,
  nextPage,
  startSize = 500,
  multiplier = 100,
  updateCurrentPage,
  canChangeLimit = true,
  totalCount = 0,
  currentDataLength = 0,
}) => {
  const theNumber = 16;
  const [showAboveFive, setShowAboveFive] = useState(false);

  // Calculate the range of entries being shown
  const startEntry = totalCount > 0 ? (currentPage - 1) * pageSize + 1 : 0;
  const endEntry = totalCount > 0 ? Math.min(currentPage * pageSize, totalCount) : 0;

  return (
    <>
      <div className="grid h-fit grid-cols-1 grid-rows-2 flex-col items-center justify-between gap-[1.5rem] pl-2 md:grid-cols-[auto_1fr_auto] md:grid-rows-1 md:flex-row">
        <div className="flex w-full justify-between md:hidden">
          <div className="">
            <span>
              {totalCount > 0 ? (
                <>
                  Showing <strong>{startEntry}</strong> to <strong>{endEntry}</strong> of <strong>{totalCount}</strong>
                </>
              ) : (
                <>
                  Page{" "}
                  <strong>
                    {+currentPage} of {pageCount}
                  </strong>{" "}
                </>
              )}
            </span>
          </div>

          <div className="">
            <LimitSelect
              pageSize={pageSize}
              multiplier={multiplier}
              startSize={startSize}
              updatePageSize={updatePageSize}
              canChangeLimit={canChangeLimit}
            />
          </div>
        </div>
        <div className="hidden w-fit min-w-fit max-w-fit justify-between whitespace-nowrap md:block">
          <span>
            {totalCount > 0 ? (
              <>
                Showing <strong>{startEntry}</strong> to <strong>{endEntry}</strong> of <strong>{totalCount}</strong> entries
              </>
            ) : (
              <>
                Page{" "}
                <strong>
                  {+currentPage} of {pageCount}
                </strong>{" "}
              </>
            )}
          </span>
        </div>

        <div
          className={`scrollbar-hide flex h-[2.5rem] w-full min-w-full max-w-full items-center gap-[.375rem] overflow-x-auto px-5  ${
            showAboveFive ? "justify-start" : "justify-start md:justify-center"
          }`}
        >
          <button
            type="button"
            onClick={previousPage}
            disabled={!canPreviousPage}
            className={`flex h-[2rem] w-[2rem] items-center justify-center rounded px-2`}
          >
            <DoubleChevronRightIcon className="rotate-180" />
          </button>
          <button
            type="button"
            disabled
            className={`flex h-[2rem] w-[2rem] items-center justify-center rounded px-2`}
          >
            <ChevronRightIcon className="rotate-180" />
          </button>

          {showAboveFive ? (
            <button
              type="button"
              className={`h-[2rem] w-[2rem] rounded border border-gray-200 px-2 shadow-md`}
              onClick={() => setShowAboveFive(false)}
            >
              ...
            </button>
          ) : null}

          <div className="flex w-fit justify-start space-x-2 ">
            {pageCount !== undefined &&
              Array.from({ length: Number(pageCount) }).map((_, index) => {
                const page = index + 1;

                if (
                  (!showAboveFive && pageCount <= 5) ||
                  (!showAboveFive && pageCount <= 7)
                ) {
                  return (
                    <button
                      type="button"
                      disabled={page === currentPage}
                      className={`h-[2rem] w-fit min-w-[2rem] max-w-fit rounded border bg-black border-gray-200 px-2 shadow-md ${
                        currentPage === page ? "bg-black" : ""
                      }`}
                      key={page}
                      onClick={() => updateCurrentPage(page)}
                    >
                      {page}
                    </button>
                  );
                }
                if (!showAboveFive && pageCount > 5 && page <= 5) {
                  return (
                    <button
                      type="button"
                      disabled={page === currentPage}
                      className={`h-[2rem] w-fit min-w-[2rem] max-w-fit rounded border bg-black border-gray-200 px-2 shadow-md ${
                        currentPage === page ? "bg-black" : ""
                      }`}
                      key={page}
                      onClick={() => updateCurrentPage(page)}
                    >
                      {page}
                    </button>
                  );
                }
                if (
                  pageCount > 5 &&
                  pageCount >= 8 &&
                  page > 5 &&
                  page < 7 &&
                  !showAboveFive
                ) {
                  return (
                    <button
                      type="button"
                      disabled={page === currentPage}
                      className={`h-[2rem] w-fit min-w-[2rem] max-w-fit rounded bg-black border border-gray-200 px-2 shadow-md ${
                        currentPage === page ? "bg-weak-100" : ""
                      }`}
                      key={page}
                      onClick={() => setShowAboveFive(true)}
                    >
                      ...
                    </button>
                  );
                }
                if (
                  !showAboveFive &&
                  pageCount > 5 &&
                  pageCount >= 8 &&
                  page === 7
                ) {
                  return (
                    <button
                      type="button"
                      disabled={page === currentPage}
                      className={`h-[2rem] w-fit min-w-[2rem] max-w-fit rounded border bg-black border-gray-200 px-2 shadow-md ${
                        currentPage === page ? "bg-black" : ""
                      }`}
                      key={page}
                      onClick={() => updateCurrentPage(page)}
                    >
                      {pageCount}
                    </button>
                  );
                }

                if (
                  showAboveFive &&
                  pageCount > 5 &&
                  pageCount >= 8 &&
                  page > 5
                ) {
                  return (
                    <button
                      type="button"
                      disabled={page === currentPage}
                      className={`h-[2rem] w-fit min-w-[2rem] max-w-fit rounded border bg-black border-gray-200 px-2 shadow-md ${
                        currentPage === page ? "bg-black" : ""
                      }`}
                      key={page}
                      onClick={() => updateCurrentPage(page)}
                    >
                      {page}
                    </button>
                  );
                }
                // return (
                //   <button
                //     className={`h-[2rem] min-w-[2rem] max-w-fit w-[2rem] rounded border border-gray-200 shadow-md ${currentPage===page?"bg-weak-100":""}`}
                //     key={page}
                //   >
                //     {page}
                //   </button>
                // );
              })}
          </div>

          <button
            type="button"
            disabled
            className={`flex h-[2rem] w-[2rem] items-center justify-center rounded px-2`}
          >
            <ChevronRightIcon />
          </button>
          <button
            type="button"
            onClick={nextPage}
            disabled={!canNextPage}
            className={`flex h-[2rem] w-[2rem] items-center justify-center rounded px-2`}
          >
            <DoubleChevronRightIcon />
          </button>
        </div>
        {/*  */}
        {/* <div>
          <button
          type="button"
          onClick={previousPage}
          disabled={!canPreviousPage}
            className={`h-10 w-10 font-bold`}
            >
            &#x02190;
            </button>{" "}
            <button
            type="button"
            onClick={nextPage}
            disabled={!canNextPage}
            className={`h-10 w-10 font-bold `}
            >
            &#x02192;
            </button>{" "}
          </div> */}
        <div className="hidden md:block">
          {/* {canChangeLimit ? ( */}
          <LimitSelect
            pageSize={pageSize}
            updatePageSize={updatePageSize}
            multiplier={multiplier}
            startSize={startSize}
            canChangeLimit={canChangeLimit}
          />
          {/* // ) : null} */}
        </div>
      </div>
    </>
  );
};

export default PaginationBar;

import React, { Fragment, memo, useCallback, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { DotIcon, KebabIcon, NarrowUpArrowIcon, Spinner } from "Assets/svgs";
import { LazyLoad } from "Components/LazyLoad";
import MkdListTableRowCol from "./MkdListTableRowCol";
import { SkeletonLoader } from "Components/Skeleton";
import { NoDataFoundImg } from "Assets/images";
import MkdListTableSelectRow from "./MkdListTableSelectRow";
import MkdListTableRightActionPanel from "./MkdListTableRightActionPanel";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { NoteModal } from "./NoteModal";
import { MkdListTableRowButtons, MkdListTableRowDropdown } from ".";

const MkdListTable = ({
  table,
  tableTitle,
  onSort,
  loading,
  setLoading,
  columns = [],
  actions,
  actionPostion = [],
  tableRole,
  deleteItem,
  deleteLoading,
  actionId = "id",
  showDeleteModal,
  currentTableData = [],
  setShowDeleteModal,
  handleTableCellChange,
  setSelectedItems,
  allowEditing,
  useImage = true,
  columnData = null,
  setColumns = null,
  setColumnData = null,
  selectedItems = [],
  allowSortColumns = true,
  onPopoverStateChange = null,
  popoverShown = false,
  maxHeight = null,
}) => {
  console.log(columns, "columnsd");
  console.log(columnData, "columnData");
  console.log(currentTableData, "currentTableData");
  const { dispatch } = React.useContext(AuthContext);
  const {
    dispatch: globalDispatch,
    state: {},
  } = React.useContext(GlobalContext);

  const [deleteId, setIdToDelete] = React.useState(null);
  const [isOneOrMoreRowSelected, setIsOneOrMoreRowSelected] =
    React.useState(false);
  const [areAllRowsSelected, setAreAllRowsSelected] = React.useState(false);
  // const [selectedIds, setSelectedIds] = React.useState([]);
  const [dragging, setDragging] = React.useState(false);
  const [fromKey, setFromKey] = React.useState(null);
  const [toKey, setToKey] = React.useState(null);
  const [selectedColumnLength, setSelectedColumnLength] = React.useState(0);
  const [note, setNote] = React.useState(null);
  const [noteModalOpen, setNoteModalOpen] = React.useState(null);
  const [shift, setShift] = React.useState({
    key: null,
    held: false,
    startingKey: false,
  });

  const showNote = (note) => {
    setNote(note);

    setNoteModalOpen(true);
  };

  const currentTableDataMemo = useMemo(
    () => JSON.stringify(currentTableData),
    [currentTableData]
  );
  const currentColumnsMemo = useMemo(
    () => JSON.stringify(columnData?.columns || columns),
    [columnData, columns]
  );

  // Removed debug logging to prevent re-renders
  const rowColumn = useMemo(
    () =>
      (columnData?.columns || columns)?.find(
        (item) => item?.accessor === "row"
      ),
    [columnData, columns]
  );

  const actionColumn = useMemo(() => {
    const columnsToUse = columnData?.columns || columns;
    return (
      (columnsToUse?.find((item) => item?.accessor === "") &&
        actions?.delete?.show) ||
      Object.keys(actions).filter(
        (key) =>
          actions[key]?.show &&
          actions[key]?.locations &&
          actions[key]?.locations?.length &&
          (actions[key]?.locations?.includes("dropdown") ||
            actions[key]?.locations?.includes("buttons"))
      )?.length
    );
  }, [columns, columnData, actions]);

  // console.log("selectedItems >>", selectedItems);
  const getPreviousSelectedIndex = useCallback(
    (tempIds) => {
      if (tempIds.length > 1) {
        // console.log("tempIds >>", tempIds, tempIds?.[tempIds?.length - 1]);
        const lastSelectedIndex = currentTableData.findIndex(
          (item) => item?.id == tempIds?.[tempIds?.length - 1]
        );
        // console.log("lastSelectedIndex >>", lastSelectedIndex);
        return lastSelectedIndex;
      } else if (tempIds.length == 1) {
        const selectedIndex = currentTableData.findIndex(
          (item) => item?.id == tempIds?.[0]
        );
        return selectedIndex;
      } else {
        return null;
      }
    },
    [currentTableData]
  );

  const getShiftRange = useCallback((currentIndex, previousKey) => {
    // console.log("currentIndex >>", currentIndex);
    // console.log("previousKey >>", previousKey);
    const lower = currentIndex < previousKey ? currentIndex : previousKey;
    const upper = currentIndex > previousKey ? currentIndex : previousKey;
    const start = lower == previousKey ? lower + 1 : lower;
    const end = upper == previousKey ? upper - 1 : upper;
    return { start, end, lower, upper };
  }, []);

  const handleSelectMultipleWithShiftKey = useCallback(
    (id, currentIndex, tempIds) => {
      const previousSelectedIndex = getPreviousSelectedIndex(tempIds);
      // console.log("previousSelectedIndex >>", previousSelectedIndex);
      if (previousSelectedIndex !== null) {
        const { lower, upper, start, end } = getShiftRange(
          currentIndex,
          previousSelectedIndex
        );

        const newIds = [...selectedItems];
        // console.log("lower >>", lower);
        // console.log("upper >>", upper);
        // console.log("currentTableData >>", currentTableData);

        for (let i = lower; i <= upper; i++) {
          // console.log("i >>", i);
          newIds.push(currentTableData[i]?.id);
        }

        setSelectedItems(newIds);
      } else {
        setSelectedItems([currentTableData?.[currentIndex]?.id]);
      }
    },
    [shift, currentTableData]
  );

  function handleSelectRow(id, index, e) {
    const tempIds = selectedItems;
    // console.log("selectedItems >>", selectedItems);
    if (actions?.select?.multiple) {
      if (e?.nativeEvent?.shiftKey) {
        handleSelectMultipleWithShiftKey(id, index, tempIds);
      } else {
        if (tempIds.includes(id)) {
          const newIds = tempIds.filter((selectedId) => selectedId !== id);
          // setSelectedIds(() => [...newIds]);
          setSelectedItems(newIds);
        } else {
          const newIds = [...tempIds, id];
          // setSelectedIds(() => [...newIds]);
          setSelectedItems(newIds);
        }
      }
    } else {
      if (tempIds.includes(id)) {
        const newIds = tempIds.filter((selectedId) => selectedId !== id);
        // setSelectedIds(() => [...newIds]);
        setSelectedItems(newIds);
      } else {
        const newIds = [id];
        // setSelectedIds(() => [...newIds]);
        setSelectedItems(newIds);
      }
    }
    // console.log(id);
  }

  const handleSelectAll = () => {
    // setAreAllRowsSelected((prevSelectAll) => !prevSelectAll);
    const allSelected = currentTableData.every((item) =>
      selectedItems.includes(item?.id)
    );

    if (!allSelected) {
      const allIds = currentTableData.map((item) => item[actionId]);
      // setSelectedIds(allIds);
      setSelectedItems(allIds);
    } else {
      // setSelectedIds([]);
      setSelectedItems([]);
    }
  };

  const handleDeleteAll = async (id) => {
    setShowDeleteModal(true);
    setIdToDelete(id);
  };

  const navigate = useNavigate();

  const setDeleteId = async (id) => {
    // console.log("id >>", id);
    setShowDeleteModal(true);
    setIdToDelete(id);
  };

  const onDragStart = (e, key) => {
    if (!allowSortColumns) return;
    // e.preventDefault();
    // console.log("onDragStart");
    setFromKey(key);
    setDragging(true);
  };
  const onDrop = (e) => {
    if (!allowSortColumns) return;
    e.preventDefault();
    if (fromKey && toKey && fromKey != toKey && columnData?.columns) {
      try {
        const tempColumns = [...columnData.columns];
        const fromColumn = tempColumns[fromKey];
        // We don't need toColumn, but we'll keep the variable for clarity
        // const toColumn = tempColumns[toKey];

        tempColumns.splice(fromKey, 1);
        tempColumns.splice(toKey, 0, fromColumn);
        if (setColumnData) {
          setColumnData((prev) => {
            return {
              ...prev,
              columns: tempColumns,
            };
          });
        }
      } catch (error) {
        console.error("Error in onDrop:", error);
      }
    }
    setToKey(null);
    setFromKey(null);
    setDragging(false);
  };
  const onDragOver = (e, key) => {
    if (!allowSortColumns) return;
    e.preventDefault();

    setToKey(key);
    // if (fromKey != key) {
    // }
  };
  const onDragEnter = (e) => {
    if (!allowSortColumns) return;
    e.preventDefault();
  };
  const onDragEnd = (e) => {
    if (!allowSortColumns) return;
    e.preventDefault();
    setToKey(null);
    setFromKey(null);
    // console.log("onDragEnd");

    setDragging(false);
  };
  const onDragLeave = (e) => {
    if (!allowSortColumns) return;
    e.preventDefault();
    setToKey(null);
    // setFromKey(null);
  };

  React.useEffect(() => {
    if (selectedItems.length <= 0) {
      setIsOneOrMoreRowSelected(false);
      setAreAllRowsSelected(false);
    }
    if (selectedItems.length === currentTableData?.length) {
      setAreAllRowsSelected(true);
    }
    if (
      selectedItems.length < currentTableData?.length &&
      selectedItems.length > 0
    ) {
      setAreAllRowsSelected(false);
    }
  }, [selectedItems?.length, currentTableDataMemo]);

  // React.useEffect(() => {
  //   const columnsToUse = columnData?.columns || columns;
  //   const length =
  //     columnsToUse?.reduce((prev, current) => {
  //       if (
  //         !["row", ""].includes(current?.accessor) &&
  //         current?.selected_column
  //       ) {
  //         return prev + 1;
  //       }
  //       return prev;
  //     }, 0) || 0;
  //   setSelectedColumnLength(length);
  // }, [currentColumnsMemo, columns]);

  // React.useEffect(() => {
  //   window.addEventListener("keyup", handleKeyUp);
  //   window.addEventListener("keydown", handleKeyDown);

  //   return () => {
  //     window.removeEventListener("keyup", handleKeyUp);
  //     window.removeEventListener("keydown", handleKeyDown);
  //   };
  // }, []);

  return (
    <LazyLoad count={7} counts={[2, 2, 2, 2, 2, 2]}>
      <div
        // style={{
        //   height: maxHeight ? `${maxHeight} !important` : null,
        //   minHeight: maxHeight ? `${maxHeight} !important` : null,
        //   maxHeight: maxHeight ? `${maxHeight} !important` : null,
        // }}
        className={`relative !h-full !max-h-full !min-h-full  !w-full min-w-full max-w-full justify-center overflow-auto !rounded-[.625rem] border border-gray-900`}
      >
        {loading ? (
          <div
            className={`max-h-fit min-h-fit w-full items-center justify-center`}
          >
            <SkeletonLoader count={7} counts={[2, 2, 2, 2, 2, 2]} />
          </div>
        ) : currentTableData?.length &&
          (columnData?.columns?.length || columns?.length) ? (
          // className="flex flex-col w-fit min-w-fit max-w-fit"

          <table className="h-fit min-w-full table-auto border-collapse divide-y divide-gray-800 rounded-md">
            <thead className="bg-gray-800">
              <tr className="!h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem]">
                {[actions?.select?.show].includes(true) || rowColumn ? (
                  <>
                    {[actions?.select?.show].includes(true) ? (
                      <th className="$ sticky -left-[0.05rem] -top-[0.05rem] z-[19] !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[2.65rem] !min-w-[2.65rem] !max-w-[2.65rem] bg-gray-100 px-[.75rem] py-[.5rem] text-xs font-medium capitalize tracking-wider text-gray-500">
                        {actions?.select?.multiple ? (
                          <input
                            type="checkbox"
                            disabled={!actions?.select?.multiple}
                            id="select_all_rows"
                            className={` focus:shadow-outline mr-1 !h-4 !w-4 cursor-pointer  rounded border border-gray-800 leading-tight text-primary accent-primary shadow focus:outline-none focus:ring-0`}
                            checked={
                              selectedItems?.length === currentTableData?.length
                            }
                            onChange={handleSelectAll}
                          />
                        ) : null}
                      </th>
                    ) : null}

                    {rowColumn ? (
                      <th
                        className={`$ sticky -top-[0.05rem] ${
                          [actions?.select?.show].includes(true)
                            ? "left-10"
                            : "left-0"
                        } z-10 !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[2.65rem] !min-w-[2.65rem] max-w-[auto] bg-gray-100 px-[.75rem] py-[.5rem] text-left text-xs font-medium capitalize tracking-wider text-gray-500`}
                      >
                        Row
                      </th>
                    ) : null}
                  </>
                ) : null}
                {/* Always render column headers, even if columnData is not properly set */}
                {(columnData?.columns?.length > 0
                  ? columnData.columns
                  : columns
                )?.map((cell, cellIndex) => {
                  if (!["row", ""].includes(cell?.accessor)) {
                    return (
                      <th
                        key={cellIndex}
                        draggable={allowSortColumns}
                        onDragStart={(e) => onDragStart(e, cellIndex)}
                        onDragEnd={onDragEnd}
                        onDragOver={(e) => onDragOver(e, cellIndex)}
                        onDragLeave={(e) => onDragLeave(e)}
                        onDrop={(e) => onDrop(e)}
                        scope="col"
                        className={`$ sticky -top-[0.05rem] z-[5] !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-[auto]  !min-w-[6.25rem] !max-w-[auto] shrink-0 grow px-6 py-[.5rem] text-left text-xs font-medium capitalize tracking-wider text-gray-500 ${
                          allowSortColumns && dragging
                            ? "cursor-grabbing"
                            : cell?.isSorted
                            ? "cursor-pointer"
                            : ""
                        } ${
                          toKey == cellIndex
                            ? "bg-primary-light"
                            : "bg-weak-100"
                        } `}
                      >
                        <div className="flex w-full items-center justify-between gap-5">
                          <div
                            className="flex grow items-center justify-between gap-5"
                            onClick={
                              cell?.isSorted
                                ? () => onSort(cellIndex)
                                : undefined
                            }
                          >
                            <div className="w-auto grow whitespace-nowrap capitalize">
                              {cell?.header?.split("_")?.join(" ")}
                            </div>
                            <span className="w-fit">
                              {cell.isSorted ? (
                                <NarrowUpArrowIcon
                                  className={`h-2 w-2 ${
                                    cell.isSortedDesc ? "rotate-180" : ""
                                  }`}
                                />
                              ) : (
                                ""
                              )}
                            </span>
                          </div>
                          {allowSortColumns ? (
                            <DotIcon className="h-2 w-2 min-w-2 max-w-2 cursor-grab" />
                          ) : null}
                        </div>
                      </th>
                    );
                  }
                  return null;
                })}
                {actionColumn ? (
                  <th className="$ sticky -right-[0.05rem] -top-[0.05rem] z-10 !h-[2.65rem] !max-h-[2.65rem] !min-h-[2.65rem] !w-fit !min-w-fit max-w-fit shrink-0 grow bg-gray-100 px-[.75rem] py-[.5rem] text-left text-xs font-medium capitalize tracking-wider text-gray-500"></th>
                ) : null}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-800 bg-gray-800">
              {currentTableData?.map((row, rowIndex) => {
                return (
                  <tr
                    className="!h-[3rem] !max-h-[3rem] !min-h-[3rem] border-b"
                    key={rowIndex}
                  >
                    {[actions?.select?.show].includes(true) || rowColumn ? (
                      <>
                        {[actions?.select?.show].includes(true) ? (
                          <td className="sticky -left-[0.05rem] z-10 !h-full !max-h-full !min-h-full !w-[2.65rem]  !min-w-[2.65rem] !max-w-[2.65rem] cursor-pointer whitespace-nowrap bg-gray-800 px-[.75rem] py-[.5rem] text-sm font-[400] capitalize leading-[1.5rem] tracking-wider text-sub-500">
                            <input
                              type="checkbox"
                              className={` focus:shadow-outline mr-1 !h-4 !w-4 cursor-pointer  rounded leading-tight text-primary accent-primary shadow focus:outline-none focus:ring-0`}
                              name="select_item"
                              checked={
                                selectedItems?.length &&
                                selectedItems.includes(row[actionId])
                              }
                              onChange={(e) =>
                                handleSelectRow(row[actionId], rowIndex, e)
                              }
                            />
                          </td>
                        ) : null}

                        {rowColumn ? (
                          <td
                            className={`sticky ${
                              [actions?.select?.show].includes(true)
                                ? "left-10"
                                : "left-0"
                            } z-[5] flex h-full w-[auto] !min-w-[2.65rem] !max-w-[auto] items-center whitespace-nowrap border-b border-gray-800 bg-gray-800 px-[.75rem] py-[.5rem] text-sm`}
                          >
                            {rowIndex + 1}
                          </td>
                        ) : null}
                      </>
                    ) : null}
                    {/* Use columns from columnData if available, otherwise use the columns prop */}
                    {(columnData?.columns?.length > 0
                      ? columnData.columns
                      : columns
                    )?.map((cell, cellIndex) => {
                      if (!["row", ""].includes(cell?.accessor)) {
                        return (
                          <MkdListTableRowCol
                            key={cellIndex}
                            columnIndex={cellIndex}
                            row={row}
                            columns={columnData?.columns || columns}
                            column={cell}
                            currentTableData={currentTableData}
                            actions={actions}
                            allowEditing={allowEditing}
                            handleSelectRow={handleSelectRow}
                            handleTableCellChange={handleTableCellChange}
                            actionPostion={actionPostion}
                            onPopoverStateChange={onPopoverStateChange}
                            selectedIds={selectedItems}
                            actionId={actionId}
                            tableRole={tableRole}
                            showNote={showNote}
                          />
                        );
                      }
                      return null;
                    })}
                    {actionColumn ? (
                      <td className="sticky -right-[0.05rem] z-[5] !w-fit !min-w-fit !max-w-fit whitespace-nowrap border-b border-b-gray-800  bg-gray-700 px-[.75rem] py-[.5rem]">
                        <div className="flex !w-fit !min-w-fit !max-w-fit items-center justify-end">
                          {actionPostion?.includes("dropdown") ? (
                            <MkdListTableRowDropdown
                              row={row}
                              actions={actions}
                              actionId={actionId}
                              setDeleteId={setDeleteId}
                              columns={columnData?.columns}
                            />
                          ) : null}
                          {actionPostion?.includes("buttons") ? (
                            <MkdListTableRowButtons
                              row={row}
                              actions={actions}
                              actionId={actionId}
                              setDeleteId={setDeleteId}
                              columns={columnData?.columns}
                            />
                          ) : null}
                        </div>
                      </td>
                    ) : null}
                  </tr>
                );
              })}
            </tbody>
          </table>
        ) : !loading && !currentTableData?.length ? (
          <div className="relative w-full">
            <div
              className={`relative max-h-fit min-h-fit w-full min-w-fit max-w-full items-center justify-center`}
            >
              <div
                className={`relative ${
                  useImage
                    ? "h-[35rem]"
                    : "flex h-[6.25rem] items-center justify-center"
                } w-full`}
              >
                {useImage ? (
                  <img
                    src={NoDataFoundImg}
                    className={`no-data-found absolute inset-x-0 m-auto h-full w-[50%] grayscale-[10%]`}
                  />
                ) : (
                  <>No Data</>
                )}
              </div>
            </div>
          </div>
        ) : null}

        {
          <LazyLoad>
            <NoteModal
              isOpen={noteModalOpen}
              note={note}
              onClose={() => {
                setNoteModalOpen(false);
                setNote(null);
              }}
            />
          </LazyLoad>
        }
      </div>
    </LazyLoad>
  );
};

export default memo(MkdListTable);

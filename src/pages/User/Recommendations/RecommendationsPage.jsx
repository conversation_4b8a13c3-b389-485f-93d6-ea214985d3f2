import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { showToast, GlobalContext } from "Context/Global";
import { useNotifications } from "../../../context/Notifications/NotificationContext";

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString();
};

const RecommendationsPage = () => {
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { clearNotification } = useNotifications();
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredRecommendations, setFilteredRecommendations] = useState([]);
  const [activeTab, setActiveTab] = useState("to-you"); // "to-you" or "by-you"

  useEffect(() => {
    loadRecommendations();
  }, [activeTab]); // Load recommendations when tab changes

  // Clear referral notifications when component mounts
  useEffect(() => {
    clearNotification('referrals');
  }, []); // Empty dependency array to run only once on mount

  useEffect(() => {
    // Apply search filter to recommendations
    if (searchQuery.trim() === "") {
      setFilteredRecommendations(recommendations);
    } else {
      const filtered = recommendations.filter((rec) =>
        rec.referral_title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        rec.referral_description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        rec.first_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        rec.last_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        rec.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (rec.community_name && rec.community_name.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredRecommendations(filtered);
    }
  }, [searchQuery, recommendations]);

  const loadRecommendations = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();

      // Use different API endpoints based on active tab
      const apiEndpoint = activeTab === "to-you"
        ? "/v1/api/dealmaker/user/my/recommendations"
        : "/v1/api/dealmaker/user/recommendations";

      const response = await sdk.callRawAPI(
        apiEndpoint,
        {},
        "GET"
      );

      if (!response.error) {
        // Sort recommendations by created_at in descending order (latest first)
        const recommendationsData = response.data || response.list || [];
        const sortedRecommendations = recommendationsData.sort((a, b) => {
          return new Date(b.created_at) - new Date(a.created_at);
        });

        setRecommendations(sortedRecommendations);
        setFilteredRecommendations(sortedRecommendations);
      } else {
        setError(response.message);
        showToast(globalDispatch, response.message || "Failed to load recommendations", 5000, "error");
      }
    } catch (err) {
      console.error("Failed to load recommendations:", err);
      setError(err.message || "Failed to load recommendations");
      showToast(globalDispatch, err.message || "Failed to load recommendations", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  // Handle viewing the opportunity details
  const handleViewReferral = (referralId) => {
    navigate(`/member/referrals/${referralId}/details`);
  };

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle accepting or rejecting a recommendation
  const handleRecommendationAction = async (referralId, accepted) => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v1/api/dealmaker/user/my/recommendations/${referralId}/update`,
        { accepted },
        "POST"
      );

      if (!response.error) {
        showToast(
          globalDispatch,
          `Recommendation ${accepted ? 'accepted' : 'rejected'} successfully`,
          5000,
          "success"
        );
        // Refresh the recommendations list
        loadRecommendations();
      } else {
        showToast(
          globalDispatch,
          response.message || `Failed to ${accepted ? 'accept' : 'reject'} recommendation`,
          5000,
          "error"
        );
      }
    } catch (err) {
      console.error("Failed to update recommendation:", err);
      showToast(
        globalDispatch,
        err.message || `Failed to ${accepted ? 'accept' : 'reject'} recommendation`,
        5000,
        "error"
      );
    }
  };

  // Current tab recommendations are just the loaded recommendations (no filtering needed)
  const currentTabRecommendations = recommendations;

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-600 text-white";
      case "completed":
        return "bg-blue-600 text-white";
      case "expired":
        return "bg-red-600 text-white";
      default:
        return "bg-gray-600 text-white";
    }
  };



  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-black">
        <div className="text-center">
          <h2 className="text-xl font-bold text-red-500 mb-4">Error</h2>
          <p className="text-[#b5b5b5] mb-4">{error}</p>
          <button
            onClick={loadRecommendations}
            className="rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea] hover:bg-[#1b5e20]"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="mx-auto max-w-7xl">
        {/* Header */}
        <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h2 className="text-xl font-bold text-[#eaeaea]">My Referrals</h2>
            <p className="text-[#b5b5b5]">
              View referrals referred to you and by you
            </p>

          </div>

          {/* Search */}
          <div className="relative">
            <input
              type="text"
              placeholder="Search referrals..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="h-[42px] w-64 rounded-lg border border-[#363636] bg-[#161616] px-4 text-[#eaeaea] placeholder-[#b5b5b5] focus:border-[#2e7d32] focus:outline-none"
            />
            <svg
              className="absolute right-3 top-3 h-4 w-4 text-[#b5b5b5]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="flex space-x-1 rounded-lg bg-[#1e1e1e] p-1">
            <button
              onClick={() => {
                setActiveTab("to-you");
                setSearchQuery(""); // Clear search when switching tabs
              }}
              className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                activeTab === "to-you"
                  ? "bg-[#2e7d32] text-white"
                  : "text-[#b5b5b5] hover:text-[#eaeaea]"
              }`}
            >
              Referred to You
            </button>
            <button
              onClick={() => {
                setActiveTab("by-you");
                setSearchQuery(""); // Clear search when switching tabs
              }}
              className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                activeTab === "by-you"
                  ? "bg-[#2e7d32] text-white"
                  : "text-[#b5b5b5] hover:text-[#eaeaea]"
              }`}
            >
              Referred by You
            </button>
          </div>
        </div>

        {/* Stats */}
        <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4">
            <h3 className="text-sm font-medium text-[#b5b5b5]">
              {activeTab === "to-you" ? "Referred to You" : "Referred by You"}
            </h3>
            <p className="text-2xl font-bold text-[#eaeaea]">{currentTabRecommendations.length}</p>
          </div>
          <div className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4">
            <h3 className="text-sm font-medium text-[#b5b5b5]">Active Opportunities</h3>
            <p className="text-2xl font-bold text-[#eaeaea]">
              {currentTabRecommendations.filter(rec => rec.referral_status === "active").length}
            </p>
          </div>
          <div className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-4">
            <h3 className="text-sm font-medium text-[#b5b5b5]">Completed Opportunities</h3>
            <p className="text-2xl font-bold text-[#eaeaea]">
              {currentTabRecommendations.filter(rec => rec.referral_status === "completed").length}
            </p>
          </div>
        </div>

        {/* Recommendations List */}
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-[#2e7d32] border-t-transparent"></div>
          </div>
        ) : filteredRecommendations.length > 0 ? (
          <div className="space-y-4">
            {filteredRecommendations.map((recommendation) => (
              <div
                key={recommendation.id}
                className="rounded-lg border border-[#363636] bg-[#1e1e1e] p-6 hover:border-[#2e7d32] transition-colors"
              >
                <div className="flex flex-col gap-4 lg:flex-row lg:items-start lg:justify-between">
                  {/* Main Content */}
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2 mb-3">
                      <h3 className="text-lg font-semibold text-[#eaeaea]">
                        {recommendation.referral_title}
                      </h3>
                      <span
                        className={`rounded-full px-2 py-1 text-xs font-medium ${getStatusBadgeColor(
                          recommendation.referral_status
                        )}`}
                      >
                        {recommendation.referral_status}
                      </span>
                    </div>

                    <p className="text-[#b5b5b5] mb-4">
                      {recommendation.referral_description}
                    </p>

                    {/* Recommender Info */}
                    <div className="flex items-center gap-3 mb-3">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-[#2e7d32] text-sm font-medium text-white">
                        {recommendation.first_name.charAt(0)}{recommendation.last_name.charAt(0)}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#eaeaea]">
                          {recommendation.first_name} {recommendation.last_name}
                        </p>
                        <p className="text-xs text-[#b5b5b5]">{recommendation.email}</p>
                      </div>
                    </div>

                    {/* Community Info */}
                    {recommendation.community_name && (
                      <div className="mb-3">
                        <span className="inline-flex items-center rounded-md bg-[#363636] px-2 py-1 text-xs text-[#eaeaea]">
                          <svg className="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                          </svg>
                          {recommendation.community_name}
                        </span>
                      </div>
                    )}

                    {/* Recommendation Data */}
                    {recommendation.recommendation_data && recommendation.recommendation_data.length > 0 && (
                      <div className="mb-3">
                        <p className="text-sm font-medium text-[#eaeaea] mb-2">Referred Candidates:</p>
                        <div className="space-y-1">
                          {recommendation.recommendation_data.map((candidate, index) => (
                            <div key={index} className="text-sm text-[#b5b5b5]">
                              • {candidate.first_name?.value} {candidate.last_name?.value} ({candidate.email?.value})
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Date */}
                    <p className="text-xs text-[#b5b5b5]">
                      Referred on {formatDate(recommendation.created_at)}
                    </p>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleViewReferral(recommendation.referral_id)}
                      className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#1b5e20] transition-colors"
                    >
                      View Opportunity
                    </button>

                    {/* Show accept/reject button only for "to-you" tab */}
                    {activeTab === "to-you" && recommendation.recommended_users && recommendation.recommended_users.length > 0 && (
                      <>
                        {recommendation.recommended_users[0].recommendation_accepted === 0 ? (
                          <button
                            onClick={() => handleRecommendationAction(recommendation.referral_id, true)}
                            className="rounded-lg bg-green-600 px-4 py-2 text-sm text-white hover:bg-green-700 transition-colors"
                          >
                            Accept
                          </button>
                        ) : recommendation.recommended_users[0].recommendation_accepted === 1 ? (
                          <button
                            onClick={() => handleRecommendationAction(recommendation.referral_id, false)}
                            className="rounded-lg bg-red-600 px-4 py-2 text-sm text-white hover:bg-red-700 transition-colors"
                          >
                            Reject
                          </button>
                        ) : (
                          <span className="rounded-lg bg-gray-600 px-4 py-2 text-sm text-white">
                            Rejected
                          </span>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <div className="mb-4 rounded-full bg-[#363636] p-3">
              <svg className="h-8 w-8 text-[#b5b5b5]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-medium text-[#eaeaea] mb-2">No referrals found</h3>
            <p className="text-[#b5b5b5] mb-4">
              {searchQuery
                ? "No referrals match your search criteria."
                : activeTab === "to-you"
                  ? "You haven't received any referrals yet."
                  : "You haven't made any referrals yet."
              }
            </p>
            {searchQuery && (
              <button
                onClick={() => setSearchQuery("")}
                className="rounded-lg bg-[#2e7d32] px-4 py-2 text-[#eaeaea] hover:bg-[#1b5e20] transition-colors"
              >
                Clear Search
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default RecommendationsPage;

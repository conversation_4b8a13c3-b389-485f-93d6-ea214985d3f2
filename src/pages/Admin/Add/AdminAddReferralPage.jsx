
import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
// import { useNavigate } from "react-router-dom";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import { InteractiveButton } from "Components/InteractiveButton";
import { Toast } from "Components/Toast";

const EmojiIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM10 18C5.58 18 2 14.42 2 10C2 5.58 5.58 2 10 2C14.42 2 18 5.58 18 10C18 14.42 14.42 18 10 18ZM7 6C7 6.82843 6.32843 7.5 5.5 7.5C4.67157 7.5 4 6.82843 4 6C4 5.17157 4.67157 4.5 5.5 4.5C6.32843 4.5 7 5.17157 7 6ZM16 6C16 6.82843 15.3284 7.5 14.5 7.5C13.6716 7.5 13 6.82843 13 6C13 5.17157 13.6716 4.5 14.5 4.5C15.3284 4.5 16 5.17157 16 6ZM10 15.5C12.33 15.5 14.32 14.05 15.12 12H4.88C5.68 14.05 7.67 15.5 10 15.5Z" fill="#B5B5B5"/>
  </svg>
);

const AttachmentIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.5 9.375L11.25 15.625C10.2083 16.6667 8.89583 17.1875 7.3125 17.1875C5.72917 17.1875 4.41667 16.6667 3.375 15.625C2.33333 14.5833 1.8125 13.2708 1.8125 11.6875C1.8125 10.1042 2.33333 8.79167 3.375 7.75L10.9375 0.1875C11.6458 -0.520833 12.5104 -0.875 13.5312 -0.875C14.5521 -0.875 15.4167 -0.520833 16.125 0.1875C16.8333 0.895833 17.1875 1.76042 17.1875 2.78125C17.1875 3.80208 16.8333 4.66667 16.125 5.375L8.5625 12.9375C8.20833 13.2917 7.78125 13.4688 7.28125 13.4688C6.78125 13.4688 6.35417 13.2917 6 12.9375C5.64583 12.5833 5.46875 12.1562 5.46875 11.6562C5.46875 11.1562 5.64583 10.7292 6 10.375L12.8125 3.5625" stroke="#B5B5B5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
);

// Custom dropdown component to replace native select
const CustomDropdown = ({ options, value, onChange, placeholder, loading }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = React.useRef(null);

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSelect = (option) => {
    onChange(option);
    setIsOpen(false);
  };

  // Find the selected option's label
  const selectedOption = options.find(opt => opt.value === value);

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Dropdown trigger button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-left text-[#eaeaea] flex items-center justify-between"
      >
        <span className={`${!selectedOption ? 'text-[#666]' : ''}`}>
          {selectedOption ? selectedOption.label : placeholder || "Select an option"}
        </span>
        <svg
          className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          fill="none"
          stroke="#b5b5b5"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute z-10 mt-1 w-full max-h-60 overflow-auto rounded-lg border border-[#363636] bg-[#242424] shadow-lg">
          {loading ? (
            <div className="flex items-center justify-center p-4">
              <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-[#eaeaea]"></div>
            </div>
          ) : options.length === 0 ? (
            <div className="p-4 text-[#b5b5b5] text-center">No options available</div>
          ) : (
            <ul className="py-1">
              {options.map((option) => (
                <li
                  key={option.value}
                  onClick={() => handleSelect(option)}
                  className={`px-4 py-2 cursor-pointer hover:bg-[#2a2a2a] text-[#eaeaea] ${
                    option.value === value ? 'bg-[#2a2a2a]' : ''
                  }`}
                >
                  {option.label}
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

const AddAdminReferralPage = ({ setSidebar }) => {
  // setSidebar is used in the Cancel button onClick handler
  // No need for custom styles anymore
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const schema = yup
    .object({
      title: yup.string().required(),
      type: yup.string().required(),
      industry: yup.string().required(),
      description: yup.string().required(),
      deal_size: yup.string().required(),
      referral_fee: yup.string().required(),
      referral_type: yup.string().required(),
      community: yup.string(),
      direct_person: yup.string(),
      additional_notes: yup.string(),
      expiration_date: yup.string()
    })
    .required();

  const { dispatch } = React.useContext(AuthContext);
  const [isSubmitLoading, setIsSubmitLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedType, setSelectedType] = useState("");
  const [communities, setCommunities] = useState([]);
  const [users, setUsers] = useState([]);
  const [fetchingUsers, setFetchingUsers] = useState(false);
  const [selectedCommunity, setSelectedCommunity] = useState("");
  const [industries, setIndustries] = useState([]);

  // Fetch communities and industries on component mount
  useEffect(() => {
    fetchCommunities();
    loadIndustries();
  }, []);

  // Load industries from API
  const loadIndustries = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI("/v1/api/dealmaker/industries", {}, "GET");

      if (!response.error && response.data) {
        setIndustries(response.data);
      } else {
        setError(response.message || "Failed to load industries");
      }
    } catch (err) {
      setError(err.message || "Failed to load industries");
    }
  };

  // Fetch communities from community_member table
  const fetchCommunities = async () => {
    try {
      console.log("Starting to fetch communities...");
      const sdk = new MkdSDK();

      // Try multiple approaches to get communities
      let communitiesFetched = false;

      // Approach 1: Try community_member table to get unique communities
      try {
        console.log("Trying community_member table for communities");
        sdk.setTable("community_member");
        const result = await sdk.callRestAPI({}, "GETALL");

        if (!result.error && result.list && result.list.length > 0) {
          console.log(`Found ${result.list.length} community members`);

          // Extract unique community IDs
          const uniqueCommunities = [];
          const communityIds = new Set();

          result.list.forEach(member => {
            if (!communityIds.has(member.community_id)) {
              communityIds.add(member.community_id);
              uniqueCommunities.push({
                id: member.community_id,
                title: `Community ${member.community_id}`
              });
            }
          });

          console.log(`Extracted ${uniqueCommunities.length} unique communities`);

          if (uniqueCommunities.length > 0) {
            // Try to get community details from community table
            try {
              sdk.setTable("community");
              const communityResult = await sdk.callRestAPI({}, "GETALL");

              if (!communityResult.error && communityResult.list && communityResult.list.length > 0) {
                console.log(`Found ${communityResult.list.length} communities in community table`);

                // Update community names where possible
                uniqueCommunities.forEach(community => {
                  const matchingCommunity = communityResult.list.find(c => c.id === community.id);
                  if (matchingCommunity && matchingCommunity.title) {
                    community.title = matchingCommunity.title;
                  }
                });
              }
            } catch (communityError) {
              console.log("Error fetching community details:", communityError);
            }

            setCommunities(uniqueCommunities);
            communitiesFetched = true;
          }
        } else {
          console.log("Community_member table returned no data or had an error");
        }
      } catch (memberError) {
        console.log("Community_member approach failed:", memberError);
      }

      // Approach 2: Try community search API
      if (!communitiesFetched) {
        try {
          console.log("Trying community search API");
          const response = await sdk.callRawAPI(
            "/v1/api/dealmaker/user/community/search",
            {},
            "GET"
          );

          if (!response.error && response.list && response.list.length > 0) {
            console.log(`Found ${response.list.length} communities with search API`);
            setCommunities(response.list);
            communitiesFetched = true;
          } else {
            console.log("Community search API returned no communities or had an error");
          }
        } catch (searchError) {
          console.log("Community search API failed:", searchError);
        }
      }

      // Approach 3: Try TreeQL with community table
      if (!communitiesFetched) {
        try {
          console.log("Trying TreeQL for communities");
          sdk.setTable("community");
          const result = await sdk.callRestAPI({}, "GETALL");

          if (!result.error && result.list && result.list.length > 0) {
            console.log(`Found ${result.list.length} communities with TreeQL`);
            setCommunities(result.list);
            communitiesFetched = true;
          } else {
            console.log("TreeQL returned no communities or had an error");
          }
        } catch (treeError) {
          console.log("TreeQL approach failed:", treeError);
        }
      }

      // If no communities found, add a default one
      if (!communitiesFetched) {
        console.log("No communities found, adding default community");
        setCommunities([{
          id: 1,
          title: "Default Community"
        }]);
      }
    } catch (err) {
      console.error("Error fetching communities:", err);
      setError(err.message || "Failed to load communities");
      tokenExpireError(dispatch, err.message);

      // Ensure we have at least one community
      setCommunities([{
        id: 1,
        title: "Default Community"
      }]);
    }
  };

  // Fetch users using community_member table
  const fetchUsers = async (communityId) => {
    try {
      setFetchingUsers(true);
      console.log(`Starting to fetch users for community ${communityId}...`);
      const sdk = new MkdSDK();

      // Use TreeQL to get community members
      sdk.setTable("community_member");
      const result = await sdk.callRestAPI({}, "GETALL");

      if (!result.error && result.list && result.list.length > 0) {
        console.log(`Found ${result.list.length} total community members`);

        // Filter members by community_id
        const communityMembers = result.list.filter(
          member => member.community_id.toString() === communityId.toString() && member.is_active === 1
        );

        console.log(`Found ${communityMembers.length} members for community ${communityId}`);

        if (communityMembers.length > 0) {
          // Now we need to get user details for each member
          const userIds = communityMembers.map(member => member.user_id);
          console.log("User IDs in this community:", userIds);

          // Try to get user details
          let userDetails = [];

          try {
            // First try to get user details from user table
            sdk.setTable("user");
            const userResult = await sdk.callRestAPI({}, "GETALL");

            if (!userResult.error && userResult.list && userResult.list.length > 0) {
              // Filter users by the IDs we found in community_member
              userDetails = userResult.list.filter(user =>
                userIds.includes(user.id)
              );

              console.log(`Found ${userDetails.length} user details`);
            }
          } catch (userError) {
            console.log("Error fetching user details:", userError);
          }

          // If we couldn't get user details, use the community_member data
          if (userDetails.length === 0) {
            userDetails = communityMembers.map(member => ({
              id: member.user_id,
              name: member.first_name && member.last_name
                ? `${member.first_name} ${member.last_name}`
                : `User ${member.user_id}`,
              community_id: member.community_id,
              role: member.role
            }));
          } else {
            // Enhance user details with community_member data
            userDetails = userDetails.map(user => {
              const memberData = communityMembers.find(m => m.user_id === user.id);
              return {
                ...user,
                community_id: memberData.community_id,
                role: memberData.role,
                // Use name from user if available, otherwise try to construct from first_name/last_name
                name: user.name || (
                  memberData.first_name && memberData.last_name
                    ? `${memberData.first_name} ${memberData.last_name}`
                    : user.email || `User ${user.id}`
                )
              };
            });
          }

          setUsers(userDetails);
          console.log("Final user list:", userDetails);
        } else {
          console.log("No members found for this community");
          setUsers([]);
        }
      } else {
        console.log("No community members found or API error");
        setUsers([]);
      }
    } catch (err) {
      console.error("Failed to fetch users:", err);
      setError(err.message || "Failed to fetch users");
      tokenExpireError(dispatch, err.message);
      setUsers([]);
    } finally {
      setFetchingUsers(false);
    }
  };

  // Handle referral type change
  const handleTypeChange = (e) => {
    const type = e.target.value;
    setSelectedType(type);

    // Reset related fields when type changes
    setSelectedCommunity("");
    setUsers([]);

    // Update form values
    setValue("referral_type", type);
    setValue("community", "");
    setValue("direct_person", "");
  };

  // Handle community change
  const handleCommunityChange = (e) => {
    const communityId = e.target.value;
    console.log(`Community changed to: ${communityId}`);

    if (!communityId) {
      console.log("No community ID provided");
      return;
    }

    setSelectedCommunity(communityId);
    setValue("community", communityId);
    setValue("community_id", communityId);

    // If direct referral, fetch users for the selected community
    if (selectedType === "direct") {
      console.log(`Fetching users for community ${communityId}`);
      fetchUsers(communityId);
    }
  };

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  // Watch the referral_type field to update the selectedType state
  const watchReferralType = watch("referral_type");

  // Update selectedType when referral_type changes
  useEffect(() => {
    if (watchReferralType) {
      setSelectedType(watchReferralType);
    }
  }, [watchReferralType]);

  const onSubmit = async (_data) => {
    setIsSubmitLoading(true);
    setError("");

    try {
      let sdk = new MkdSDK();
      sdk.setTable("referral");

      // Format date as YYYY-MM-DD 00:00:00 for created_at and updated_at
      const currentDate = new Date().toISOString().split('T')[0] + ' 00:00:00';

      // Determine referral type for API
      let referralTypeForAPI;
      switch (_data.referral_type) {
        case "open":
          referralTypeForAPI = "open referral";
          break;
        case "community":
          referralTypeForAPI = "community referral";
          break;
        case "direct":
          referralTypeForAPI = "direct referral";
          break;
        default:
          referralTypeForAPI = "open referral";
      }

      // Log form data for debugging
      console.log("Form data:", _data);

      // Ensure we have valid IDs by parsing them
      const directPersonId = _data.referral_type === "direct" && _data.direct_person
        ? parseInt(_data.direct_person, 10)
        : 0;

      // For direct referrals, we might have already set the community_id when selecting the user
      let communityId = 0;

      if (_data.referral_type === "direct") {
        // For direct referrals, try to get community_id from the selected user
        const selectedUser = users.find(u =>
          (u.user_id || u.id).toString() === _data.direct_person
        );

        if (selectedUser && selectedUser.community_id) {
          communityId = parseInt(selectedUser.community_id, 10);
          console.log(`Using community_id ${communityId} from selected user`);
        } else if (_data.community) {
          communityId = parseInt(_data.community, 10);
        }
      } else if (_data.referral_type === "community" && _data.community) {
        communityId = parseInt(_data.community, 10);
      }

      const industryId = _data.industry
        ? parseInt(_data.industry, 10)
        : null;

      console.log("Parsed IDs:", {
        directPersonId,
        communityId,
        industryId
      });

      // Map the form data to the API expected format based on the database schema
      const apiData = {
        user_id: directPersonId || 1,
        reposted_from: 0,
        job_title: _data.title,
        title: _data.title, // Include both title and job_title with the same data
        description: _data.description,
        description_image: "",
        pay: _data.referral_fee, // Commission percent
        referred_to_id: directPersonId || 0,
        industry_id: industryId,
        is_active: 1, // tinyint(1) with default 1
        type: _data.type,
        client_details: _data.additional_notes || "",
        completed_by: null,
        status: "active", // Default status
        created_at: currentDate,
        expiration_date: _data.expiration_date
          ? new Date(_data.expiration_date).toISOString().split('T')[0] + ' 00:00:00'
          : new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0] + ' 00:00:00', // Format: YYYY-MM-DD 00:00:00
        updated_at: currentDate,
        deal_size: _data.deal_size,
        payment_method: "bank",
        referral_type: referralTypeForAPI,
        additional_notes: _data.additional_notes || null,
        community_id: communityId
      };

      // Log API data for debugging
      console.log("API data:", apiData);

      const result = await sdk.callRestAPI(
        apiData,
        "POST"
      );

      if (!result.error) {
        showToast(globalDispatch, "Opportunity Added Successfully");

        // First, dispatch the REFRESH_DATA action to trigger a refresh of the table
        console.log("Dispatching REFRESH_DATA action to refresh the table");
        globalDispatch({
          type: "REFRESH_DATA",
          payload: {
            refreshData: true,
            timestamp: new Date().getTime() // Add timestamp to ensure uniqueness
          },
        });

        // Then close the sidebar modal with a slight delay to ensure the refresh is triggered
        setTimeout(() => {
          setSidebar(false);
        }, 100);
      } else {
        setError(result.message || "Failed to add opportunity");
      }
      setIsSubmitLoading(false);
    } catch (error) {
      setIsSubmitLoading(false);
      console.log("Error", error);
      setError(error.message || "Failed to add opportunity");
      tokenExpireError(dispatch, error.message);
    }
  };

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "referral",
      },
    });
  }, []);

  return (
    <div className="w-full h-full overflow-y-auto rounded-lg bg-[#1e1e1e] p-6">
      <style>
        {`
          input[type="date"]::-webkit-calendar-picker-indicator {
            filter: invert(1);
            cursor: pointer;
          }
          input[type="date"]::-webkit-calendar-picker-indicator:hover {
            filter: invert(1) brightness(1.2);
          }
        `}
      </style>
      <h2 className="mb-6 text-xl font-bold text-[#eaeaea]">Add Opportunity</h2>
      {/* Note: The AdminHeader component is not visible here because this component is rendered inside a modal sidebar */}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 max-w-2xl mx-auto">
        <div>
          <label className="block text-sm text-[#b5b5b5]">Title</label>
          <input
            type="text"
            {...register("title")}
            placeholder="Enter referral title"
            className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
          />
          {errors.title && <p className="mt-1 text-xs text-red-500">{errors.title.message}</p>}
        </div>

        <div>
          <label className="block text-sm text-[#b5b5b5]">Type of Opportunity</label>
          <Controller
            name="type"
            control={control}
            render={({ field }) => (
              <CustomDropdown
                options={[
                  { value: "looking_for_service", label: "Looking for Service" },
                  { value: "looking_for_product", label: "Looking for Product" },
                  { value: "looking_for_buyer", label: "Looking for Buyer" },
                  { value: "looking_for_investor", label: "Looking for Investor" },
                  { value: "looking_for_partner", label: "Looking for Partner" }
                ]}
                value={field.value}
                onChange={(option) => {
                  field.onChange(option.value);
                }}
                placeholder="Select type"
              />
            )}
          />
          {errors.type && <p className="mt-1 text-xs text-red-500">{errors.type.message}</p>}
        </div>

        <div>
          <label className="block text-sm text-[#b5b5b5]">Industry</label>
          <Controller
            name="industry"
            control={control}
            render={({ field }) => (
              <CustomDropdown
                options={industries.map((industry) => ({
                  value: industry.id.toString(),
                  label: industry.name
                }))}
                value={field.value}
                onChange={(option) => {
                  field.onChange(option.value);
                }}
                placeholder="Select industry"
                loading={industries.length === 0}
              />
            )}
          />
          {errors.industry && <p className="mt-1 text-xs text-red-500">{errors.industry.message}</p>}
        </div>

        <div>
          <label className="block text-sm text-[#b5b5b5]">Description</label>
          <div className="relative">

            <textarea
              {...register("description")}
              placeholder="Write your text here..."
              rows={4}
              className="mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea]"
            />
          </div>
          {errors.description && <p className="mt-1 text-xs text-red-500">{errors.description.message}</p>}
        </div>



        <div>
          <label className="block text-sm text-[#b5b5b5]">Estimated Deal Size</label>
          <input
            type="number"
            {...register("deal_size")}
            placeholder="e.g. 500"
            min="0"
            step="0.01"
            className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
          />
          {errors.deal_size && <p className="mt-1 text-xs text-red-500">{errors.deal_size.message}</p>}
        </div>

        <div>
          <label className="block text-sm text-[#b5b5b5]">Commission Percentage (%)</label>
          <input
            type="number"
            {...register("referral_fee")}
            placeholder="Enter percentage (e.g. 10, 15)"
            min="0"
            max="100"
            step="0.01"
            className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
          />
          {errors.referral_fee && <p className="mt-1 text-xs text-red-500">{errors.referral_fee.message}</p>}
        </div>

        <div>
          <label className="block text-sm text-[#b5b5b5]">Referral Type</label>
          <Controller
            name="referral_type"
            control={control}
            render={({ field }) => (
              <CustomDropdown
                options={[
                  { value: "open", label: "Open Referral" },
                  { value: "community", label: "Community Referral" },
                  { value: "direct", label: "Direct Referral" }
                ]}
                value={field.value}
                onChange={(option) => {
                  field.onChange(option.value);
                  handleTypeChange({ target: { value: option.value } });
                }}
                placeholder="Select type"
              />
            )}
          />
          {errors.referral_type && <p className="mt-1 text-xs text-red-500">{errors.referral_type.message}</p>}
        </div>

        <div className="grid grid-cols-2 gap-4">
          {/* Show community selection for community and direct referrals */}
          {(selectedType === "community" || selectedType === "direct") && (
            <div>
              <label className="block text-sm text-[#b5b5b5]">Select Community</label>
              <div className="space-y-2">
                <Controller
                  name="community"
                  control={control}
                  render={({ field }) => (
                    <CustomDropdown
                      options={communities.map(community => {
                        // Handle different data structures that might come from different APIs
                        const communityId = community.id?.value || community.id || community.community_id;

                        // Try to get the community name from various possible fields
                        let communityName = null;

                        if (community.title?.value) {
                          communityName = community.title.value;
                        } else if (community.title) {
                          communityName = community.title;
                        } else if (community.name?.value) {
                          communityName = community.name.value;
                        } else if (community.name) {
                          communityName = community.name;
                        } else {
                          communityName = `Community ${communityId}`;
                        }

                        return {
                          value: communityId.toString(),
                          label: communityName
                        };
                      })}
                      value={field.value}
                      onChange={(option) => {
                        field.onChange(option.value);
                        handleCommunityChange({ target: { value: option.value } });
                      }}
                      placeholder="Select community"
                      loading={communities.length === 0}
                    />
                  )}
                />

                {/* Debug button to manually fetch communities */}
                {communities.length <= 1 && (
                  <button
                    type="button"
                    onClick={() => {
                      console.log("Manual fetch communities triggered");
                      fetchCommunities();
                    }}
                    className="text-xs text-[#b5b5b5] hover:text-[#eaeaea]"
                  >
                    Limited communities found. Click to retry.
                  </button>
                )}
              </div>
              {errors.community && <p className="mt-1 text-xs text-red-500">{errors.community.message}</p>}
            </div>
          )}

          {/* Show direct person selection only for direct referrals */}
          {selectedType === "direct" && (
            <div>
              <label className="block text-sm text-[#b5b5b5]">Select Direct Person</label>
              {fetchingUsers ? (
                <div className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea] flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-[#eaeaea]"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  <Controller
                    name="direct_person"
                    control={control}
                    render={({ field }) => (
                      <CustomDropdown
                        options={users.map(user => {
                          // Get user ID from the user object
                          const userId = user.user_id || user.id;

                          // Get user name with role information
                          let userName = user.name;
                          if (!userName) {
                            userName = user.first_name && user.last_name
                              ? `${user.first_name} ${user.last_name}`
                              : user.email || user.username || `User ${userId}`;
                          }

                          // Add role information if available
                          if (user.role) {
                            userName = `${userName} (${user.role})`;
                          }

                          return {
                            value: userId.toString(),
                            label: userName,
                            community_id: user.community_id
                          };
                        })}
                        value={field.value}
                        onChange={(option) => {
                          field.onChange(option.value);
                          // Store selected user's community_id for API payload
                          const selectedUser = users.find(u =>
                            (u.user_id || u.id).toString() === option.value
                          );
                          if (selectedUser) {
                            setValue("community_id", selectedUser.community_id);
                          }
                        }}
                        placeholder="Select person"
                        loading={fetchingUsers}
                      />
                    )}
                  />

                  {/* Debug button to manually fetch users */}
                  {users.length === 0 && (
                    <button
                      type="button"
                      onClick={() => {
                        console.log("Manual fetch users triggered");
                        fetchUsers(selectedCommunity);
                      }}
                      className="text-xs text-[#b5b5b5] hover:text-[#eaeaea]"
                    >
                      No users found. Select a community with members.
                    </button>
                  )}
                </div>
              )}
              {errors.direct_person && <p className="mt-1 text-xs text-red-500">{errors.direct_person.message}</p>}
            </div>
          )}
        </div>

        <div>
          <label className="block text-sm text-[#b5b5b5]">Additional Notes</label>
          <textarea
            {...register("additional_notes")}
            placeholder="Write any additional notes here..."
            rows={4}
            className="mt-1 w-full rounded-lg border border-[#363636] bg-[#242424] p-4 text-[#eaeaea]"
          />
          {errors.additional_notes && <p className="mt-1 text-xs text-red-500">{errors.additional_notes.message}</p>}
        </div>

        <div>
          <label className="block text-sm text-[#b5b5b5]">Expiration Date</label>
          <input
            type="date"
            {...register("expiration_date")}
            className="mt-1 h-10 w-full rounded-lg border border-[#363636] bg-[#242424] px-4 text-[#eaeaea]"
          />
          {/* <p className="mt-1 text-xs text-[#b5b5b5]">If not set, defaults to 1 year from today</p> */}
          {errors.expiration_date && <p className="mt-1 text-xs text-red-500">{errors.expiration_date.message}</p>}
        </div>

        <div className="flex justify-end gap-4">
          <button
            type="button"
            onClick={() => {
              // Close sidebar
              if (setSidebar) {
                setSidebar(false);
              }
            }}
            className="rounded-lg border border-[#363636] px-4 py-2 text-sm text-[#eaeaea]"
          >
            Cancel
          </button>
          <InteractiveButton
            type="submit"
            loading={isSubmitLoading}
            disabled={isSubmitLoading}
            className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm text-[#eaeaea]"
          >
            Submit Opportunity
          </InteractiveButton>
        </div>
      </form>

      {error && <Toast message={error} />}
    </div>
  );
};

export default AddAdminReferralPage;